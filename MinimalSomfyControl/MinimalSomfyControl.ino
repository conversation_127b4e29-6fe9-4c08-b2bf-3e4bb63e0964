// ============== Minimal Somfy RTS Controller for ESP32 ==============
#include <Preferences.h>
#include <ELECHOUSE_CC1101_SRC_DRV.h>

// --- Configuration ---
// 1. Define the pins connecting your ESP32 to the CC1101 module.
const uint8_t PIN_SCK   = 18;
const uint8_t PIN_MISO  = 19;
const uint8_t PIN_MOSI  = 23;
const uint8_t PIN_CSN   = 5;
const uint8_t PIN_TX    = 13; // GDO0 on the CC1101

// 2. Define the radio frequency (confirmed from your remote capture).
const float RADIO_FREQUENCY = 433.42;

// --- Global Variables ---
Preferences preferences;
uint32_t remoteAddress;
uint16_t rollingCode;
bool isPaired = false;

// --- Function Declarations ---
void buildFrame(byte* frame, uint8_t button);
void sendCommand(uint8_t button, uint8_t repeat);
void resetRollingCode();

void setup() {
  Serial.begin(115200);
  while (!Serial) { ; }
  Serial.println("\n\nESP32 Somfy RTS Controller");

  // --- Initialize CC1101 Radio ---
  Serial.println("Initializing CC1101...");
  pinMode(PIN_TX, OUTPUT);
  ELECHOUSE_cc1101.setSpiPin(PIN_SCK, PIN_MISO, PIN_MOSI, PIN_CSN);
  ELECHOUSE_cc1101.Init();
  ELECHOUSE_cc1101.setModulation(2); // ASK/OOK
  ELECHOUSE_cc1101.setMHZ(RADIO_FREQUENCY);
  ELECHOUSE_cc1101.SetTx();
  if (ELECHOUSE_cc1101.getCC1101()) {
      Serial.printf("CC1101 initialized successfully at %.2f MHz.\n", RADIO_FREQUENCY);
  } else {
      Serial.println("FATAL: CC1101 initialization failed! Check wiring.");
      while(true) delay(1000);
  }

  // --- Load or Create Remote Configuration ---
  preferences.begin("somfy_remote", false); // Open Preferences in read-write mode

  // Get a unique remote address from the ESP32's MAC address if not already set
  remoteAddress = preferences.getUInt("remote_addr", 0);
  if (remoteAddress == 0) {
    remoteAddress = ESP.getEfuseMac() & 0x00FFFFFF;
    preferences.putUInt("remote_addr", remoteAddress);
    Serial.printf("Generated a new unique remote address: 0x%X\n", remoteAddress);
  }

  // Load the last saved rolling code and paired status
  rollingCode = preferences.getUShort("rolling_code", 0);
  isPaired = preferences.getBool("is_paired", false);
  
  preferences.end(); // Close preferences for now

  Serial.printf("Remote Address: 0x%X\n", remoteAddress);
  Serial.printf("Last Saved Rolling Code: %d\n", rollingCode);
  Serial.printf("Paired Status: %s\n", isPaired ? "PAIRED" : "NOT PAIRED");

  Serial.println("\n--- Ready for Commands ---");
  Serial.println("  'u' -> Move Shade Up");
  Serial.println("  'd' -> Move Shade Down");
  Serial.println("  'm' -> MY / Stop");
  Serial.println("  'p' -> PAIR (Sends Up+Down for 0.5s)");
  Serial.println("  'y' -> YES, it's paired (Saves the paired state)");
  Serial.println("  'r' -> RESET rolling code to 0");
  Serial.println("--------------------------");
}

void loop() {
  if (Serial.available() > 0) {
    char command = tolower(Serial.read());

    switch (command) {
      case 'u':
        Serial.println("Sending command: UP");
        sendCommand(0x2, 2); // 0x2 is the command code for UP
        break;
      
      case 'd':
        Serial.println("Sending command: DOWN");
        sendCommand(0x4, 2); // 0x4 is the command code for DOWN
        break;

      case 'm':
        Serial.println("Sending command: MY / STOP");
        sendCommand(0x1, 2); // 0x1 is the command code for MY/STOP
        break;

      case 'p':
        // UPDATED PAIRING LOGIC
        Serial.println("Sending command: PAIR (Up+Down for 0.5s)");
        Serial.println("--> To Pair: Power cycle the sha de motor. Within 2 minutes, send this command.");
        Serial.println("--> The motor should jog to confirm pairing.");
        {
          unsigned long startTime = millis();
          // Send the Up+Down command repeatedly for 500 milliseconds
          while (millis() - startTime < 500) {
            sendCommand(0x6, 1); // 0x6 is Up+Down. Send with only 1 repeat to fit more commands in the time window.
            delay(40); // Small delay between sends
          }
        }
        break;

      case 'y':
        if (!isPaired) {
            Serial.println("OK. Shade is now marked as PAIRED.");
            Serial.println("Rolling codes will now be saved permanently.");
            isPaired = true;
            // Save the paired status and current rolling code to permanent memory
            preferences.begin("somfy_remote", false);
            preferences.putBool("is_paired", true);
            preferences.putUShort("rolling_code", rollingCode);
            preferences.end();
        } else {
            Serial.println("Shade is already marked as paired.");
        }
        break;

      case 'r':
        Serial.println("RESETTING rolling code to 0...");
        resetRollingCode();
        break;
    }
  }
}

// Resets the rolling code to 0 in both memory and permanent storage.
void resetRollingCode() {
  rollingCode = 0;
  isPaired = false; // Un-pairing also makes sense on reset
  preferences.begin("somfy_remote", false);
  preferences.putUShort("rolling_code", 0);
  preferences.putBool("is_paired", false);
  preferences.end();
  Serial.println("Rolling code has been reset to 0 and the device is marked as UNPAIRED.");
}

// Increments the rolling code and builds the 7-byte frame.
void buildFrame(byte* frame, uint8_t button) {
  // Increment the rolling code
  rollingCode++;
  Serial.printf("Using rolling code: %d\n", rollingCode);

  // If the device is paired, save the new code to permanent storage
  if (isPaired) {
    preferences.begin("somfy_remote", false);
    preferences.putUShort("rolling_code", rollingCode);
    preferences.end();
  }

  // 1. Assemble the 7-byte payload
  uint8_t encKey = 0xA0 | (rollingCode & 0x0F);
  frame[0] = encKey;
  frame[1] = button << 4;
  frame[2] = rollingCode >> 8;
  frame[3] = rollingCode;
  frame[4] = remoteAddress >> 16;
  frame[5] = remoteAddress >> 8;
  frame[6] = remoteAddress;

  // 2. Calculate and add the checksum
  byte checksum = 0;
  for (byte i = 0; i < 7; i++) {
    checksum = checksum ^ frame[i] ^ (frame[i] >> 4);
  }
  frame[1] |= checksum & 0x0F;

  // 3. Obfuscate the payload
  for (byte i = 1; i < 7; i++) {
    frame[i] ^= frame[i - 1];
  }
}

// Transmits the command frame over the radio.
void sendCommand(uint8_t button, uint8_t repeat) {
  byte frame[7];
  buildFrame(frame, button);

  ELECHOUSE_cc1101.SetTx();

  // Wakeup Pulse & Silence (critical for the first transmission)
  digitalWrite(PIN_TX, HIGH);
  delayMicroseconds(9415);
  digitalWrite(PIN_TX, LOW);
  delayMicroseconds(89565);

  // Send the first frame with 2 hardware syncs
  for (int i = 0; i < 2; i++) {
    digitalWrite(PIN_TX, HIGH); delayMicroseconds(2560);
    digitalWrite(PIN_TX, LOW); delayMicroseconds(2560);
  }
  // Software sync
  digitalWrite(PIN_TX, HIGH); delayMicroseconds(4550);
  digitalWrite(PIN_TX, LOW); delayMicroseconds(640);
  // Data payload
  for (byte i = 0; i < 56; i++) {
    if (((frame[i/8] >> (7 - (i%8))) & 1)) {
      digitalWrite(PIN_TX, LOW); delayMicroseconds(640);
      digitalWrite(PIN_TX, HIGH); delayMicroseconds(640);
    } else {
      digitalWrite(PIN_TX, HIGH); delayMicroseconds(640);
      digitalWrite(PIN_TX, LOW); delayMicroseconds(640);
    }
  }
  digitalWrite(PIN_TX, LOW);

  // Send the remaining repeats
  for (int r = 1; r < repeat; r++) {
    delayMicroseconds(30415); // Inter-frame silence
    // Repeats use 7 hardware syncs
    for (int i = 0; i < 7; i++) {
      digitalWrite(PIN_TX, HIGH); delayMicroseconds(2560);
      digitalWrite(PIN_TX, LOW); delayMicroseconds(2560);
    }
    // Software sync
    digitalWrite(PIN_TX, HIGH); delayMicroseconds(4550);
    digitalWrite(PIN_TX, LOW); delayMicroseconds(640);
    // Data payload
    for (byte i = 0; i < 56; i++) {
      if (((frame[i/8] >> (7 - (i%8))) & 1)) {
        digitalWrite(PIN_TX, LOW); delayMicroseconds(640);
        digitalWrite(PIN_TX, HIGH); delayMicroseconds(640);
      } else {
        digitalWrite(PIN_TX, HIGH); delayMicroseconds(640);
        digitalWrite(PIN_TX, LOW); delayMicroseconds(640);
      }
    }
    digitalWrite(PIN_TX, LOW);
  }
  
  ELECHOUSE_cc1101.setSidle();
}
