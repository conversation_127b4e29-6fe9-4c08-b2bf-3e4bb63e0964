name: Check Code Format

on: [push, pull_request]

jobs:
  lint:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    - name: Check src format
      uses: DoozyX/clang-format-lint-action@v0.18
      with:
        source: './src'
        extensions: 'h,cpp'
        clangFormatVersion: 9
    - name: Check examples format
      uses: DoozyX/clang-format-lint-action@v0.18 
      with:
        source: './examples'
        extensions: 'h,cpp,ino'
        clangFormatVersion: 9
