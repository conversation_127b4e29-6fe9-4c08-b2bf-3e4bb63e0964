<doxygenlayout version="1.0">
  <!-- Generated by doxygen 1.8.17 -->
  <!-- Navigation index tabs for HTML output -->
  <navindex>
    <tab type="mainpage" visible="yes" title="Introduction"></tab>
    <tab type="@DOXY_API_DOCS_TAB_TYPE@" visible="yes" title="API Documentation" intro="These are the libraries supplied in the Raspberry Pi Pico SDK"/>
    <tab type="user" url="@ref examples_page" visible="yes" title="Examples" intro="Links to SDK examples"/>
    <tab type="usergroup" url="@ref weblinks_page" visible="yes" title="Additional Documentation" intro="Links to datasheets and documentation">
      <tab type="user" url="https://rptl.io/pico-datasheet" visible="yes" title="Raspberry Pi Pico Datasheet" intro=""/>
      <tab type="user" url="https://rptl.io/picow-datasheet" visible="yes" title="Raspberry Pi Pico W Datasheet" intro=""/>
      <tab type="user" url="https://rptl.io/rp2040-datasheet" visible="yes" title="RP2040 Datasheet" intro=""/>
      <tab type="user" url="https://rptl.io/rp2040-design" visible="yes" title="Hardware design with RP2040" intro=""/>
      <tab type="user" url="https://rptl.io/pico-c-sdk" visible="yes" title="Raspberry Pi Pico C/C++ SDK" intro=""/>
      <tab type="user" url="https://rptl.io/pico-micropython" visible="yes" title="Raspberry Pi Pico Python SDK" intro=""/>
      <tab type="user" url="https://rptl.io/pico-get-started" visible="yes" title="Getting started with Raspberry Pi Pico" intro=""/>
      <tab type="user" url="https://rptl.io/picow-connect" visible="yes" title="Connecting to the Internet with Raspberry Pi Pico W" intro=""/>
    </tab>
    <tab type="usergroup" url="@ref weblinks_page" visible="yes" title="Web" intro="useful weblinks">
      <tab type="user" url="https://www.raspberrypi.com/" visible="yes" title="Raspberry Pi Site" intro=""/>
      <tab type="user" url="https://rptl.io/rp2040-get-started" visible="yes" title="Raspberry Pi Pico Page" intro=""/>
      <tab type="user" url="https://forums.raspberrypi.com/" visible="yes" title="Raspberry Pi Forums" intro=""/>
      <tab type="user" url="https://github.com/raspberrypi/pico-sdk" visible="yes" title="Raspberry Pi Pico SDK on GitHub" intro=""/>
      <tab type="user" url="https://github.com/raspberrypi/pico-examples" visible="yes" title="Pico Examples on GitHub" intro=""/>
      <tab type="user" url="https://github.com/raspberrypi/pico-extras" visible="yes" title="Pico Extras on GitHub" intro=""/>
      <tab type="user" url="https://github.com/raspberrypi/pico-playground" visible="yes" title="Pico Playground on GitHub" intro=""/>
      <tab type="user" url="https://github.com/raspberrypi/pico-bootrom" visible="yes" title="Pico Bootrom on GitHub" intro=""/>
    </tab>
    <tab type="pages" visible="no" title="" intro=""/>
    <tab type="namespaces" visible="yes" title="">
      <tab type="namespacelist" visible="yes" title="" intro=""/>
      <tab type="namespacemembers" visible="yes" title="" intro=""/>
    </tab>
    <tab type="interfaces" visible="yes" title="">
      <tab type="interfacelist" visible="yes" title="" intro=""/>
      <tab type="interfaceindex" visible="$ALPHABETICAL_INDEX" title=""/>
      <tab type="interfacehierarchy" visible="yes" title="" intro=""/>
    </tab>
    <tab type="classes" visible="no" title="">
      <tab type="classlist" visible="yes" title="" intro=""/>
      <tab type="classindex" visible="$ALPHABETICAL_INDEX" title=""/>
      <tab type="hierarchy" visible="yes" title="" intro=""/>
      <tab type="classmembers" visible="yes" title="" intro=""/>
    </tab>
    <tab type="structs" visible="yes" title="">
      <tab type="structlist" visible="yes" title="" intro=""/>
      <tab type="structindex" visible="$ALPHABETICAL_INDEX" title=""/>
    </tab>
    <tab type="exceptions" visible="yes" title="">
      <tab type="exceptionlist" visible="yes" title="" intro=""/>
      <tab type="exceptionindex" visible="$ALPHABETICAL_INDEX" title=""/>
      <tab type="exceptionhierarchy" visible="yes" title="" intro=""/>
    </tab>
    <tab type="files" visible="no" title="">
      <tab type="filelist" visible="yes" title="" intro=""/>
      <tab type="globals" visible="yes" title="" intro=""/>
    </tab>
    <tab type="examples" visible="yes" title="" intro=""/>
  </navindex>

  <!-- Layout definition for a class page -->
  <class>
    <briefdescription visible="yes"/>
    <includes visible="$SHOW_INCLUDE_FILES"/>
    <inheritancegraph visible="$CLASS_GRAPH"/>
    <collaborationgraph visible="$COLLABORATION_GRAPH"/>
    <memberdecl>
      <nestedclasses visible="yes" title=""/>
      <publictypes title=""/>
      <services title=""/>
      <interfaces title=""/>
      <publicslots title=""/>
      <signals title=""/>
      <publicmethods title=""/>
      <publicstaticmethods title=""/>
      <publicattributes title=""/>
      <publicstaticattributes title=""/>
      <protectedtypes title=""/>
      <protectedslots title=""/>
      <protectedmethods title=""/>
      <protectedstaticmethods title=""/>
      <protectedattributes title=""/>
      <protectedstaticattributes title=""/>
      <packagetypes title=""/>
      <packagemethods title=""/>
      <packagestaticmethods title=""/>
      <packageattributes title=""/>
      <packagestaticattributes title=""/>
      <properties title=""/>
      <events title=""/>
      <privatetypes title=""/>
      <privateslots title=""/>
      <privatemethods title=""/>
      <privatestaticmethods title=""/>
      <privateattributes title=""/>
      <privatestaticattributes title=""/>
      <friends title=""/>
      <related title="" subtitle=""/>
      <membergroups visible="yes"/>
    </memberdecl>
    <detaileddescription title=""/>
    <memberdef>
      <inlineclasses title=""/>
      <typedefs title=""/>
      <enums title=""/>
      <services title=""/>
      <interfaces title=""/>
      <constructors title=""/>
      <functions title=""/>
      <related title=""/>
      <variables title=""/>
      <properties title=""/>
      <events title=""/>
    </memberdef>
    <allmemberslink visible="yes"/>
    <usedfiles visible="$SHOW_USED_FILES"/>
    <authorsection visible="yes"/>
  </class>

  <!-- Layout definition for a namespace page -->
  <namespace>
    <briefdescription visible="yes"/>
    <memberdecl>
      <nestednamespaces visible="yes" title=""/>
      <constantgroups visible="yes" title=""/>
      <interfaces visible="yes" title=""/>
      <classes visible="yes" title=""/>
      <structs visible="yes" title=""/>
      <exceptions visible="yes" title=""/>
      <typedefs title=""/>
      <sequences title=""/>
      <dictionaries title=""/>
      <enums title=""/>
      <functions title=""/>
      <variables title=""/>
      <membergroups visible="yes"/>
    </memberdecl>
    <detaileddescription title=""/>
    <memberdef>
      <inlineclasses title=""/>
      <typedefs title=""/>
      <sequences title=""/>
      <dictionaries title=""/>
      <enums title=""/>
      <functions title=""/>
      <variables title=""/>
    </memberdef>
    <authorsection visible="yes"/>
  </namespace>

  <!-- Layout definition for a file page -->
  <file>
    <briefdescription visible="yes"/>
    <includes visible="$SHOW_INCLUDE_FILES"/>
    <includegraph visible="$INCLUDE_GRAPH"/>
    <includedbygraph visible="$INCLUDED_BY_GRAPH"/>
    <sourcelink visible="yes"/>
    <memberdecl>
      <interfaces visible="yes" title=""/>
      <classes visible="yes" title=""/>
      <structs visible="yes" title=""/>
      <exceptions visible="yes" title=""/>
      <namespaces visible="yes" title=""/>
      <constantgroups visible="yes" title=""/>
      <defines title=""/>
      <typedefs title=""/>
      <sequences title=""/>
      <dictionaries title=""/>
      <enums title=""/>
      <functions title=""/>
      <variables title=""/>
      <membergroups visible="yes"/>
    </memberdecl>
    <detaileddescription title=""/>
    <memberdef>
      <inlineclasses title=""/>
      <defines title=""/>
      <typedefs title=""/>
      <sequences title=""/>
      <dictionaries title=""/>
      <enums title=""/>
      <functions title=""/>
      <variables title=""/>
    </memberdef>
    <authorsection/>
  </file>

  <!-- Layout definition for a group page -->
  <group>
    <briefdescription visible="yes"/>
    <groupgraph visible="$GROUP_GRAPHS"/>
    <memberdecl>
      <nestedgroups visible="yes" title=""/>
      <dirs visible="yes" title=""/>
      <files visible="yes" title=""/>
      <namespaces visible="yes" title=""/>
      <classes visible="yes" title=""/>
      <defines title=""/>
      <typedefs title=""/>
      <sequences title=""/>
      <dictionaries title=""/>
      <enums title=""/>
      <enumvalues title=""/>
      <functions title=""/>
      <variables title=""/>
      <signals title=""/>
      <publicslots title=""/>
      <protectedslots title=""/>
      <privateslots title=""/>
      <events title=""/>
      <properties title=""/>
      <friends title=""/>
      <membergroups visible="yes"/>
    </memberdecl>
    <detaileddescription title=""/>
    <memberdef>
      <pagedocs/>
      <inlineclasses title=""/>
      <defines title=""/>
      <typedefs title=""/>
      <sequences title=""/>
      <dictionaries title=""/>
      <enums title=""/>
      <enumvalues title=""/>
      <functions title=""/>
      <variables title=""/>
      <signals title=""/>
      <publicslots title=""/>
      <protectedslots title=""/>
      <privateslots title=""/>
      <events title=""/>
      <properties title=""/>
      <friends title=""/>
    </memberdef>
    <authorsection visible="yes"/>
  </group>

  <!-- Layout definition for a directory page -->
  <directory>
    <briefdescription visible="yes"/>
    <directorygraph visible="yes"/>
    <memberdecl>
      <dirs visible="yes"/>
      <files visible="yes"/>
    </memberdecl>
    <detaileddescription title=""/>
  </directory>
</doxygenlayout>
