## Documentation and datasheets {#weblinks_page}

The full documentation for the RP2040 and Raspberry Pi Pico board can be found at the following links

 - [RP2040 Datasheet](https://rptl.io/rp2040-datasheet)
 - [Raspberry Pi Pico Datasheet](https://rptl.io/pico-datasheet)
 - [RP235x Datasheet](https://rptl.io/rp2040-datasheet)
 - [Raspberry Pi Pico2 Datasheet](https://rptl.io/pico-datasheet)
 - [Raspberry Pi Pico W Datasheet](https://rptl.io/picow-datasheet)
 - [Hardware design with RP2040](https://rptl.io/rp2040-design)
 - [Raspberry Pi Pico C/C++ SDK](https://rptl.io/pico-c-sdk)
 - [Raspberry Pi Pico Python SDK](https://rptl.io/pico-micropython)
 - [Getting started with Raspberry Pi Pico](https://rptl.io/pico-get-started)
 - [Connecting to the Internet with Raspberry Pi Pico W](https://rptl.io/picow-connect)

### Weblinks

At Raspberry Pi we have a very community-based attitude to help. We run a very popular and busy forum where you can ask questions about any aspect of the Raspberry Pi ecosystem, including the Raspberry Pi Pico.

You can find our forums at the [following link](https://forums.raspberrypi.com/).

For the main Raspberry Pi website, [see here](https://www.raspberrypi.com)

For the Raspberry Pi Pico page, [see here](https://rptl.io/rp2040-get-started)

### GitHub

All the source code for the Raspberry Pi Pico SDK, examples and other libraries can be found on GitHub.

 - [Raspberry Pi Pico SDK](https://github.com/raspberrypi/pico-sdk)
 - [Pico Examples](https://github.com/raspberrypi/pico-examples)
 - [Pico Extras - Libraries under development](https://github.com/raspberrypi/pico-extras)
 - [Pico Playground - Examples that use Pico Extras](https://github.com/raspberrypi/pico-playground)
 - [Pico Bootrom source code](https://github.com/raspberrypi/pico-bootrom)
