## Examples Index {#examples_page}

This page links to the various example code fragments in this documentation. For more complete examples, please see the [pico-examples](https://github.com/raspberrypi/pico-examples) repository, which contains complete buildable projects.

 - [RTC example](@ref rtc_example)
 - [UART example](@ref uart_example)
 - [ADC example](@ref adc_example)
 - [I2C example](@ref i2c_example)
 - [Clock example](@ref clock_example)
 - [Timer example](@ref timer_example)
 - [Flash programming example](@ref flash_example)
 - [Watchdog example](@ref watchdog_example)
 - [Divider example](@ref divider_example)
 - [PWM example](@ref pwm_example)
 - [Multicore example](@ref multicore_example)
 - [Reset example](@ref reset_example)


All examples are "Copyright (c) 2020 Raspberry Pi (Trading) Ltd", and are released under a 3-Clause BSD licence. Briefly, this means you are free to use the example code
as long as you retain the copyright notice. Full details on the licence can be found [here](https://opensource.org/licenses/BSD-3-Clause).

