function(pico_message param)
    if (${ARGC} EQUAL 1)
        message("${param}")
        return()
    endif ()

    if (NOT ${ARGC} EQUAL 2)
        message(FATAL_ERROR "Expect at most 2 arguments")
    endif ()
    message("${param}" "${ARGV1}")
endfunction()

macro(assert VAR MSG)
    if (NOT ${VAR})
        message(FATAL_ERROR "${MSG}")
    endif ()
endmacro()

function(pico_find_in_paths OUT PATHS NAME)
    foreach(PATH IN LISTS ${PATHS})
        if (EXISTS ${PATH}/${NAME})
            get_filename_component(FULLNAME ${PATH}/${NAME} ABSOLUTE)
            set(${OUT} ${FULLNAME} PARENT_SCOPE)
            return()
        endif()
    endforeach()
    set(${OUT} "" PARENT_SCOPE)
endfunction()