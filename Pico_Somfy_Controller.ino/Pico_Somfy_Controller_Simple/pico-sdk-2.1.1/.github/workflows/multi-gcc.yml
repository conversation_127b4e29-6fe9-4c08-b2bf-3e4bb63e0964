
name: Multi GCC
on:
  workflow_dispatch:
  push:
    branches:
      - 'master'
      - 'test_workflow'

jobs:
  build:
    if: github.repository_owner == 'raspberrypi'
    runs-on: [self-hosted, Linux, X64]

    steps:
    - name: Clean workspace
      run: |
        echo "Cleaning up previous run"
        rm -rf "${{ github.workspace }}"
        mkdir -p "${{ github.workspace }}"

    - name: Checkout repo
      uses: actions/checkout@v4

    - name: Checkout submodules
      run: git submodule update --init

    - name: Host Release
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=host; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: Host Debug
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=host; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 6.2.1 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-6_2-2016q4; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 6.2.1 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-6_2-2016q4; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 6.3.1 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-6-2017-q2-update; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 6.3.1 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-6-2017-q2-update; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 7.2.1 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-7-2017-q4-major; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 7.2.1 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-7-2017-q4-major; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 7.3.1 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-7-2018-q2-update; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 7.3.1 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-7-2018-q2-update; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 8.2.1 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-8-2018-q4-major; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 8.2.1 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-8-2018-q4-major; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 8.3.1 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-8-2019-q3-update; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 8.3.1 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-8-2019-q3-update; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 9.2.1 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-9-2019-q4-major; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 9.2.1 Debug RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-9-2019-q4-major; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 9.2.1 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-9-2019-q4-major; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 9.2.1 Release RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-9-2019-q4-major; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 9.3.1 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-9-2020-q2-update; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 9.3.1 Debug RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-9-2020-q2-update; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 9.3.1 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-9-2020-q2-update; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 9.3.1 Release RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-9-2020-q2-update; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 10.2.1 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-10-2020-q4-major; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 10.2.1 Debug RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-10-2020-q4-major; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 10.2.1 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-10-2020-q4-major; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 10.2.1 Release RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-10-2020-q4-major; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 10.3.1 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-10.3-2021.10; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 10.3.1 Debug RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-10.3-2021.10; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 10.3.1 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-10.3-2021.10; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 10.3.1 Release RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-none-eabi-10.3-2021.10; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 11.2.1 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-11.2-2022.02-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 11.2.1 Debug RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-11.2-2022.02-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 11.2.1 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-11.2-2022.02-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 11.2.1 Release RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/gcc-arm-11.2-2022.02-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 11.3.1 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-11.3.rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 11.3.1 Debug RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-11.3.rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 11.3.1 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-11.3.rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 11.3.1 Release RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-11.3.rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 12.2.1 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-12.2.rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 12.2.1 Debug RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-12.2.rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 12.2.1 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-12.2.rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 12.2.1 Release RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-12.2.rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 12.3.1 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-12.3.rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 12.3.1 Debug RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-12.3.rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 12.3.1 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-12.3.rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 12.3.1 Release RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-12.3.rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 13.2.1 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-13.2.Rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 13.2.1 Debug RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-13.2.Rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 13.2.1 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-13.2.Rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 13.2.1 Release RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-13.2.Rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 13.3.1 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-13.3.rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 13.3.1 Debug RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-13.3.rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 13.3.1 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-13.3.rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 13.3.1 Release RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-13.3.rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: LLVM 14.0.0 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_COMPILER=pico_arm_clang -DPICO_TOOLCHAIN_PATH=/opt/arm/LLVMEmbeddedToolchainForArm-14.0.0; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: LLVM 14.0.0 Debug RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_COMPILER=pico_arm_clang -DPICO_TOOLCHAIN_PATH=/opt/arm/LLVMEmbeddedToolchainForArm-14.0.0; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: LLVM 14.0.0 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_COMPILER=pico_arm_clang -DPICO_TOOLCHAIN_PATH=/opt/arm/LLVMEmbeddedToolchainForArm-14.0.0; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: LLVM 14.0.0 Release RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_COMPILER=pico_arm_clang -DPICO_TOOLCHAIN_PATH=/opt/arm/LLVMEmbeddedToolchainForArm-14.0.0; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: RISCV GCC 14.1.0 Debug RP2350 RISCV
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350-riscv -DPICO_TOOLCHAIN_PATH=/opt/riscv/riscv32-unknown-elf-gcc-14.1.0; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: RISCV GCC 14.1.0 Release RP2350 RISCV
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350-riscv -DPICO_TOOLCHAIN_PATH=/opt/riscv/riscv32-unknown-elf-gcc-14.1.0; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 14.2.1 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 14.2.1 Debug RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 14.2.1 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: GCC 14.2.1 Release RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_TOOLCHAIN_PATH=/opt/arm/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: LLVM 15.0.2 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_COMPILER=pico_arm_clang -DPICO_TOOLCHAIN_PATH=/opt/arm/LLVMEmbeddedToolchainForArm-15.0.2; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: LLVM 15.0.2 Debug RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_COMPILER=pico_arm_clang -DPICO_TOOLCHAIN_PATH=/opt/arm/LLVMEmbeddedToolchainForArm-15.0.2; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: LLVM 15.0.2 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_COMPILER=pico_arm_clang -DPICO_TOOLCHAIN_PATH=/opt/arm/LLVMEmbeddedToolchainForArm-15.0.2; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: LLVM 15.0.2 Release RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_COMPILER=pico_arm_clang -DPICO_TOOLCHAIN_PATH=/opt/arm/LLVMEmbeddedToolchainForArm-15.0.2; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: LLVM 16.0.0 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_COMPILER=pico_arm_clang -DPICO_TOOLCHAIN_PATH=/opt/arm/LLVMEmbeddedToolchainForArm-16.0.0; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: LLVM 16.0.0 Debug RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_COMPILER=pico_arm_clang -DPICO_TOOLCHAIN_PATH=/opt/arm/LLVMEmbeddedToolchainForArm-16.0.0; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: LLVM 16.0.0 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_COMPILER=pico_arm_clang -DPICO_TOOLCHAIN_PATH=/opt/arm/LLVMEmbeddedToolchainForArm-16.0.0; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: LLVM 16.0.0 Release RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_COMPILER=pico_arm_clang -DPICO_TOOLCHAIN_PATH=/opt/arm/LLVMEmbeddedToolchainForArm-16.0.0; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: LLVM 17.0.1 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_COMPILER=pico_arm_clang -DPICO_TOOLCHAIN_PATH=/opt/arm/LLVMEmbeddedToolchainForArm-17.0.1; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: LLVM 17.0.1 Debug RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_COMPILER=pico_arm_clang -DPICO_TOOLCHAIN_PATH=/opt/arm/LLVMEmbeddedToolchainForArm-17.0.1; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: LLVM 17.0.1 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_COMPILER=pico_arm_clang -DPICO_TOOLCHAIN_PATH=/opt/arm/LLVMEmbeddedToolchainForArm-17.0.1; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: LLVM 17.0.1 Release RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_COMPILER=pico_arm_clang -DPICO_TOOLCHAIN_PATH=/opt/arm/LLVMEmbeddedToolchainForArm-17.0.1; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: LLVM 18.1.3 Debug Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_COMPILER=pico_arm_clang -DPICO_TOOLCHAIN_PATH=/opt/arm/LLVM-ET-Arm-18.1.3-Linux-x86_64; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: LLVM 18.1.3 Debug RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Debug -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_COMPILER=pico_arm_clang -DPICO_TOOLCHAIN_PATH=/opt/arm/LLVM-ET-Arm-18.1.3-Linux-x86_64; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: LLVM 18.1.3 Release Pico W
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_BOARD=pico_w -DPICO_PLATFORM=rp2040 -DPICO_COMPILER=pico_arm_clang -DPICO_TOOLCHAIN_PATH=/opt/arm/LLVM-ET-Arm-18.1.3-Linux-x86_64; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

    - name: LLVM 18.1.3 Release RP2350
      if: ${{ !cancelled() }}
      shell: bash
      run: cd ${{github.workspace}}; mkdir -p build; rm -rf build/*; cd build; cmake ../ -DPICO_SDK_TESTS_ENABLED=1 -DCMAKE_BUILD_TYPE=Release -DPICO_NO_PICOTOOL=1 -DPICO_PLATFORM=rp2350 -DPICO_COMPILER=pico_arm_clang -DPICO_TOOLCHAIN_PATH=/opt/arm/LLVM-ET-Arm-18.1.3-Linux-x86_64; make --output-sync=target --no-builtin-rules --no-builtin-variables -j$(nproc)

