name: CMake
on:
  push:
    branches-ignore:
      - 'iar/**'
  pull_request:

env:
  # Customize the CMake build type here (Release, Debug, RelWithDebInfo, etc.)
  BUILD_TYPE: Release

jobs:
  build:
    # cannot specify filter for pull_request in on: above, so do it here
    if: github.repository_owner == 'raspberrypi' && (github.event_name != 'pull_request' || !startsWith(github.head_ref, 'iar/'))
    runs-on: [self-hosted, Linux, X64]

    steps:
    - name: Clean workspace
      run: |
        echo "Cleaning up previous run"
        rm -rf "${{ github.workspace }}"
        mkdir -p "${{ github.workspace }}"

    - name: Checkout repo
      uses: actions/checkout@v4

    - name: Checkout submodules
      run: git submodule update --init

    - name: Create Build Environment
      # Some projects don't allow in-source building, so create a separate build directory
      # We'll use this as our working directory for all subsequent commands
      run: cmake -E make_directory ${{github.workspace}}/build

    - name: Configure CMake
      # Use a bash shell so we can use the same syntax for environment variable
      # access regardless of the host operating system
      shell: bash
      working-directory: ${{github.workspace}}/build
      # Note the current convention is to use the -S and -B options here to specify source
      # and build directories, but this is only available with CMake 3.13 and higher.
      # The CMake binaries on the Github Actions machines are (as of this writing) 3.12
      run: cmake $GITHUB_WORKSPACE -DCMAKE_BUILD_TYPE=$BUILD_TYPE -DPICO_SDK_TESTS_ENABLED=1

    - name: Build
      working-directory: ${{github.workspace}}/build
      shell: bash
      # Execute the build.  You can specify a specific target with "--target <NAME>"
      run: cmake --build . --config $BUILD_TYPE --parallel $(nproc)
